image: atlassian/default-image:3 # The base Docker image for the pipeline environment

pipelines:
  default:
    - step:
        name: Install Dependencies         
        caches:
          - node 
        script:
          - echo "Installing Node.js dependencies..."
          - npm install 
          - echo "Dependencies installed."
    - step:
        name: Run Tests
        script:
          - echo "Running application tests..."
          - npm test
          - echo "Tests complete."
    - step:
        name: Build Application (optional for simple apps)
        script:
          - echo "Simulating build process..."
          - npm run build
          - echo "Build simulation finished."