const { add, multiply, greet } = require('./app.js');

// Simple test framework
let testsPassed = 0;
let testsTotal = 0;

function test(description, testFunction) {
    testsTotal++;
    try {
        testFunction();
        console.log(`✓ ${description}`);
        testsPassed++;
    } catch (error) {
        console.log(`✗ ${description}`);
        console.log(`  Error: ${error.message}`);
    }
}

function assertEqual(actual, expected, message) {
    if (actual !== expected) {
        throw new Error(`${message || 'Assertion failed'}: expected ${expected}, got ${actual}`);
    }
}

// Run tests
console.log('Running Node.js App Tests...\n');

test('add function should correctly add two numbers', () => {
    assertEqual(add(2, 3), 5, 'add(2, 3) should equal 5');
    assertEqual(add(-1, 1), 0, 'add(-1, 1) should equal 0');
    assertEqual(add(0, 0), 0, 'add(0, 0) should equal 0');
});

test('multiply function should correctly multiply two numbers', () => {
    assertEqual(multiply(3, 4), 12, 'multiply(3, 4) should equal 12');
    assertEqual(multiply(-2, 5), -10, 'multiply(-2, 5) should equal -10');
    assertEqual(multiply(0, 100), 0, 'multiply(0, 100) should equal 0');
});

test('greet function should return proper greeting', () => {
    assertEqual(greet('World'), 'Hello, World!', 'greet("World") should return "Hello, World!"');
    assertEqual(greet('Alice'), 'Hello, Alice!', 'greet("Alice") should return "Hello, Alice!"');
    assertEqual(greet(''), 'Hello, !', 'greet("") should return "Hello, !"');
});

// Test edge cases
test('add function should handle decimal numbers', () => {
    assertEqual(add(1.5, 2.5), 4, 'add(1.5, 2.5) should equal 4');
});

test('multiply function should handle decimal numbers', () => {
    assertEqual(multiply(2.5, 4), 10, 'multiply(2.5, 4) should equal 10');
});

// Summary
console.log(`\nTest Results: ${testsPassed}/${testsTotal} tests passed`);

if (testsPassed === testsTotal) {
    console.log('🎉 All tests passed!');
    process.exit(0);
} else {
    console.log('❌ Some tests failed!');
    process.exit(1);
}
