const http = require('http');
const url = require('url');

// Simple utility functions for testing
function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

function greet(name) {
    return `Hello, ${name}!`;
}

// Create a simple HTTP server
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const query = parsedUrl.query;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Content-Type', 'application/json');

    if (path === '/') {
        res.writeHead(200);
        res.end(JSON.stringify({
            message: 'Welcome to the Node.js Test App!',
            version: '1.0.0',
            endpoints: [
                '/health',
                '/add?a=5&b=3',
                '/multiply?a=4&b=7',
                '/greet?name=World'
            ]
        }));
    } else if (path === '/health') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'OK',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        }));
    } else if (path === '/add') {
        const a = parseFloat(query.a) || 0;
        const b = parseFloat(query.b) || 0;
        const result = add(a, b);
        res.writeHead(200);
        res.end(JSON.stringify({
            operation: 'addition',
            a: a,
            b: b,
            result: result
        }));
    } else if (path === '/multiply') {
        const a = parseFloat(query.a) || 0;
        const b = parseFloat(query.b) || 0;
        const result = multiply(a, b);
        res.writeHead(200);
        res.end(JSON.stringify({
            operation: 'multiplication',
            a: a,
            b: b,
            result: result
        }));
    } else if (path === '/greet') {
        const name = query.name || 'Anonymous';
        const message = greet(name);
        res.writeHead(200);
        res.end(JSON.stringify({
            greeting: message,
            name: name
        }));
    } else {
        res.writeHead(404);
        res.end(JSON.stringify({
            error: 'Not Found',
            message: 'The requested endpoint does not exist'
        }));
    }
});

const PORT = process.env.PORT || 3000;

// Only start server if this file is run directly (not during testing)
if (require.main === module) {
    server.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
        console.log(`Visit http://localhost:${PORT} to see the app`);
    });
}

// Export functions for testing
module.exports = {
    add,
    multiply,
    greet,
    server
};
