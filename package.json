{"name": "node-app", "version": "1.0.0", "description": "A simple Node.js app for testing Bitbucket pipelines", "main": "app.js", "scripts": {"start": "node app.js", "test": "node test.js", "build": "echo \"Build process completed successfully\" && if not exist dist mkdir dist && copy app.js dist\\", "dev": "node app.js"}, "keywords": ["nodejs", "testing", "bitbucket", "pipeline"], "author": "", "license": "MIT", "engines": {"node": ">=14.0.0"}}